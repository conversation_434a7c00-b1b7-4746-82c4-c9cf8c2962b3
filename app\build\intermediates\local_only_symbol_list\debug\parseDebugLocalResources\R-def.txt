R_DEF: Internal format may change without notice
local
color background_color
color background_l1
color black
color button_blue_fill
color content_background
color fragment_background
color header_background
color otc_fitter_item_unselect_color
color purple_200
color purple_500
color purple_700
color release_button_background
color release_button_text_color
color status_bar_color
color tab_background
color tab_indicator_color
color tab_selected_text_color
color tab_text_color
color teal_200
color teal_700
color text_l3
color text_primary
color text_secondary
color transparent
color white
dimen dimen_10
dimen dimen_12
dimen dimen_16
dimen dimen_20
dimen dimen_200
dimen dimen_25
dimen dimen_40
dimen dimen_8
dimen global_corner_radius_2
dimen global_text_size_12
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable item_background
drawable release_button_background
id appBarLayout
id clLayout
id coIndicator
id fluent_container
id fluent_content_nsv
id fluent_refresh_layout
id home_feed_linear_tabLayout
id home_navigation_ll
id home_release_btn
id home_status_bar
id home_viewPager
id main_content
id navigation_container
id recyclerView
id rl_new_hand_area_animation_layer
id sticky_release_btn
id sticky_tab_container
id sticky_tab_layout
id textView
id webview_pool_container
layout activity_main
layout fragment_content
layout item_content
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string n_send_comment
style Theme.Demo
xml backup_rules
xml data_extraction_rules
