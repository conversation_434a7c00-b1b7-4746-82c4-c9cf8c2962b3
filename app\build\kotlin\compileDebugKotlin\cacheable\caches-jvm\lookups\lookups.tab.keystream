  AppBarLayout android.app.Activity  ContentFragment android.app.Activity  Handler android.app.Activity  Int android.app.Activity  IntArray android.app.Activity  Looper android.app.Activity  Math android.app.Activity  R android.app.Activity  	TabLayout android.app.Activity  TabLayoutMediator android.app.Activity  View android.app.Activity  
ViewPager2 android.app.Activity  ViewPagerAdapter android.app.Activity  animateTabToOneThirdPosition android.app.Activity  newInstance android.app.Activity  onCreate android.app.Activity  stickyTabLayout android.app.Activity  	tabLayout android.app.Activity  until android.app.Activity  
viewPager2 android.app.Activity  LayoutParams !android.app.Activity.AppBarLayout  OnTabSelectedListener android.app.Activity.TabLayout  Tab android.app.Activity.TabLayout  OnPageChangeCallback android.app.Activity.ViewPager2  Context android.content  AppBarLayout android.content.Context  ContentFragment android.content.Context  Handler android.content.Context  Int android.content.Context  IntArray android.content.Context  Looper android.content.Context  Math android.content.Context  R android.content.Context  	TabLayout android.content.Context  TabLayoutMediator android.content.Context  View android.content.Context  
ViewPager2 android.content.Context  ViewPagerAdapter android.content.Context  animateTabToOneThirdPosition android.content.Context  newInstance android.content.Context  stickyTabLayout android.content.Context  	tabLayout android.content.Context  until android.content.Context  
viewPager2 android.content.Context  LayoutParams $android.content.Context.AppBarLayout  OnTabSelectedListener !android.content.Context.TabLayout  Tab !android.content.Context.TabLayout  OnPageChangeCallback "android.content.Context.ViewPager2  AppBarLayout android.content.ContextWrapper  ContentFragment android.content.ContextWrapper  Handler android.content.ContextWrapper  Int android.content.ContextWrapper  IntArray android.content.ContextWrapper  Looper android.content.ContextWrapper  Math android.content.ContextWrapper  R android.content.ContextWrapper  	TabLayout android.content.ContextWrapper  TabLayoutMediator android.content.ContextWrapper  View android.content.ContextWrapper  
ViewPager2 android.content.ContextWrapper  ViewPagerAdapter android.content.ContextWrapper  animateTabToOneThirdPosition android.content.ContextWrapper  newInstance android.content.ContextWrapper  stickyTabLayout android.content.ContextWrapper  	tabLayout android.content.ContextWrapper  until android.content.ContextWrapper  
viewPager2 android.content.ContextWrapper  LayoutParams +android.content.ContextWrapper.AppBarLayout  OnTabSelectedListener (android.content.ContextWrapper.TabLayout  Tab (android.content.ContextWrapper.TabLayout  OnPageChangeCallback )android.content.ContextWrapper.ViewPager2  displayMetrics android.content.res.Resources  Bundle 
android.os  Handler 
android.os  Looper 
android.os  getInt android.os.BaseBundle  putInt android.os.BaseBundle  getInt android.os.Bundle  let android.os.Bundle  putInt android.os.Bundle  postDelayed android.os.Handler  
getMainLooper android.os.Looper  AttributeSet android.util  heightPixels android.util.DisplayMetrics  LayoutInflater android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  AppBarLayout  android.view.ContextThemeWrapper  ContentFragment  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  Math  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  	TabLayout  android.view.ContextThemeWrapper  TabLayoutMediator  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
ViewPager2  android.view.ContextThemeWrapper  ViewPagerAdapter  android.view.ContextThemeWrapper  animateTabToOneThirdPosition  android.view.ContextThemeWrapper  newInstance  android.view.ContextThemeWrapper  stickyTabLayout  android.view.ContextThemeWrapper  	tabLayout  android.view.ContextThemeWrapper  until  android.view.ContextThemeWrapper  
viewPager2  android.view.ContextThemeWrapper  LayoutParams -android.view.ContextThemeWrapper.AppBarLayout  OnTabSelectedListener *android.view.ContextThemeWrapper.TabLayout  Tab *android.view.ContextThemeWrapper.TabLayout  OnPageChangeCallback +android.view.ContextThemeWrapper.ViewPager2  from android.view.LayoutInflater  inflate android.view.LayoutInflater  GONE android.view.View  OnClickListener android.view.View  OnScrollChangeListener android.view.View  VISIBLE android.view.View  alpha android.view.View  animate android.view.View  context android.view.View  findViewById android.view.View  getLocationOnScreen android.view.View  height android.view.View  layoutParams android.view.View  setOnClickListener android.view.View  setOnScrollChangeListener android.view.View  top android.view.View  translationY android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> (android.view.View.OnScrollChangeListener  context android.view.ViewGroup  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  translationY !android.view.ViewPropertyAnimator  
withEndAction !android.view.ViewPropertyAnimator  withStartAction !android.view.ViewPropertyAnimator  TextView android.widget  text android.widget.TextView  AppBarLayout #androidx.activity.ComponentActivity  ContentFragment #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  IntArray #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  Math #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  	TabLayout #androidx.activity.ComponentActivity  TabLayoutMediator #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
ViewPager2 #androidx.activity.ComponentActivity  ViewPagerAdapter #androidx.activity.ComponentActivity  animateTabToOneThirdPosition #androidx.activity.ComponentActivity  newInstance #androidx.activity.ComponentActivity  stickyTabLayout #androidx.activity.ComponentActivity  	tabLayout #androidx.activity.ComponentActivity  until #androidx.activity.ComponentActivity  
viewPager2 #androidx.activity.ComponentActivity  LayoutParams 0androidx.activity.ComponentActivity.AppBarLayout  OnTabSelectedListener -androidx.activity.ComponentActivity.TabLayout  Tab -androidx.activity.ComponentActivity.TabLayout  OnPageChangeCallback .androidx.activity.ComponentActivity.ViewPager2  AppCompatActivity androidx.appcompat.app  AppBarLayout (androidx.appcompat.app.AppCompatActivity  ContentFragment (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  IntArray (androidx.appcompat.app.AppCompatActivity  Looper (androidx.appcompat.app.AppCompatActivity  Math (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  	TabLayout (androidx.appcompat.app.AppCompatActivity  TabLayoutMediator (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
ViewPager2 (androidx.appcompat.app.AppCompatActivity  ViewPagerAdapter (androidx.appcompat.app.AppCompatActivity  animateTabToOneThirdPosition (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  newInstance (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	resources (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  stickyTabLayout (androidx.appcompat.app.AppCompatActivity  	tabLayout (androidx.appcompat.app.AppCompatActivity  until (androidx.appcompat.app.AppCompatActivity  
viewPager2 (androidx.appcompat.app.AppCompatActivity  LayoutParams 5androidx.appcompat.app.AppCompatActivity.AppBarLayout  OnTabSelectedListener 2androidx.appcompat.app.AppCompatActivity.TabLayout  Tab 2androidx.appcompat.app.AppCompatActivity.TabLayout  OnPageChangeCallback 3androidx.appcompat.app.AppCompatActivity.ViewPager2  CoordinatorLayout !androidx.coordinatorlayout.widget  Behavior 3androidx.coordinatorlayout.widget.CoordinatorLayout  height 3androidx.coordinatorlayout.widget.CoordinatorLayout  AppBarLayout <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  Math <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  
ViewCompat <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  AppBarLayout #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ContentFragment #androidx.core.app.ComponentActivity  CoordinatorLayout #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  FragmentActivity #androidx.core.app.ComponentActivity  FragmentStateAdapter #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  Math #androidx.core.app.ComponentActivity  NestedScrollView #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  SmartRefreshLayout #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  	TabLayout #androidx.core.app.ComponentActivity  TabLayoutMediator #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
ViewPager2 #androidx.core.app.ComponentActivity  ViewPagerAdapter #androidx.core.app.ComponentActivity  animateTabToOneThirdPosition #androidx.core.app.ComponentActivity  newInstance #androidx.core.app.ComponentActivity  stickyTabLayout #androidx.core.app.ComponentActivity  	tabLayout #androidx.core.app.ComponentActivity  until #androidx.core.app.ComponentActivity  
viewPager2 #androidx.core.app.ComponentActivity  LayoutParams 0androidx.core.app.ComponentActivity.AppBarLayout  OnTabSelectedListener -androidx.core.app.ComponentActivity.TabLayout  Tab -androidx.core.app.ComponentActivity.TabLayout  OnPageChangeCallback .androidx.core.app.ComponentActivity.ViewPager2  
ViewCompat androidx.core.view  SCROLL_AXIS_VERTICAL androidx.core.view.ViewCompat  NestedScrollView androidx.core.widget  OnScrollChangeListener %androidx.core.widget.NestedScrollView  setOnScrollChangeListener %androidx.core.widget.NestedScrollView  <SAM-CONSTRUCTOR> <androidx.core.widget.NestedScrollView.OnScrollChangeListener  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  ARG_POSITION androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  ContentAdapter androidx.fragment.app.Fragment  ContentFragment androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  LinearLayoutManager androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  RecyclerView androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  
ViewHolder androidx.fragment.app.Fragment  	arguments androidx.fragment.app.Fragment  context androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  
mutableListOf androidx.fragment.app.Fragment  onCreate androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  AppBarLayout &androidx.fragment.app.FragmentActivity  ContentFragment &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  IntArray &androidx.fragment.app.FragmentActivity  Looper &androidx.fragment.app.FragmentActivity  Math &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  	TabLayout &androidx.fragment.app.FragmentActivity  TabLayoutMediator &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
ViewPager2 &androidx.fragment.app.FragmentActivity  ViewPagerAdapter &androidx.fragment.app.FragmentActivity  animateTabToOneThirdPosition &androidx.fragment.app.FragmentActivity  newInstance &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  stickyTabLayout &androidx.fragment.app.FragmentActivity  	tabLayout &androidx.fragment.app.FragmentActivity  until &androidx.fragment.app.FragmentActivity  
viewPager2 &androidx.fragment.app.FragmentActivity  LayoutParams 3androidx.fragment.app.FragmentActivity.AppBarLayout  OnTabSelectedListener 0androidx.fragment.app.FragmentActivity.TabLayout  Tab 0androidx.fragment.app.FragmentActivity.TabLayout  OnPageChangeCallback 1androidx.fragment.app.FragmentActivity.ViewPager2  LinearLayoutManager androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  ContentFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  
ViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  newInstance 1androidx.recyclerview.widget.RecyclerView.Adapter  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  FragmentStateAdapter androidx.viewpager2.adapter  ContentFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  newInstance 0androidx.viewpager2.adapter.FragmentStateAdapter  
ViewPager2 androidx.viewpager2.widget  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  currentItem %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  stickyTabLayout :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  	tabLayout :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  AppBarLayout "com.google.android.material.appbar  Behavior /com.google.android.material.appbar.AppBarLayout  LayoutParams /com.google.android.material.appbar.AppBarLayout  OnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  addOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  top /com.google.android.material.appbar.AppBarLayout  totalScrollRange /com.google.android.material.appbar.AppBarLayout  translationY /com.google.android.material.appbar.AppBarLayout  onDependentViewChanged 8com.google.android.material.appbar.AppBarLayout.Behavior  
onLayoutChild 8com.google.android.material.appbar.AppBarLayout.Behavior  SCROLL_FLAG_ENTER_ALWAYS <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SNAP <com.google.android.material.appbar.AppBarLayout.LayoutParams  scrollFlags <com.google.android.material.appbar.AppBarLayout.LayoutParams  	TabLayout  com.google.android.material.tabs  TabLayoutMediator  com.google.android.material.tabs  OnTabSelectedListener *com.google.android.material.tabs.TabLayout  Tab *com.google.android.material.tabs.TabLayout  addOnTabSelectedListener *com.google.android.material.tabs.TabLayout  addTab *com.google.android.material.tabs.TabLayout  getTabAt *com.google.android.material.tabs.TabLayout  newTab *com.google.android.material.tabs.TabLayout  	selectTab *com.google.android.material.tabs.TabLayout  position .com.google.android.material.tabs.TabLayout.Tab  setText .com.google.android.material.tabs.TabLayout.Tab  text .com.google.android.material.tabs.TabLayout.Tab  TabConfigurationStrategy 2com.google.android.material.tabs.TabLayoutMediator  attach 2com.google.android.material.tabs.TabLayoutMediator  <SAM-CONSTRUCTOR> Kcom.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy  SmartRefreshLayout com.scwang.smart.refresh.layout  animate 2com.scwang.smart.refresh.layout.SmartRefreshLayout  setOnRefreshListener 2com.scwang.smart.refresh.layout.SmartRefreshLayout  translationY 2com.scwang.smart.refresh.layout.SmartRefreshLayout  
RefreshLayout #com.scwang.smart.refresh.layout.api  
finishRefresh 1com.scwang.smart.refresh.layout.api.RefreshLayout  OnRefreshListener (com.scwang.smart.refresh.layout.listener  <SAM-CONSTRUCTOR> :com.scwang.smart.refresh.layout.listener.OnRefreshListener  ARG_POSITION com.ttv.demo  AppBarLayout com.ttv.demo  AppCompatActivity com.ttv.demo  AttributeSet com.ttv.demo  Boolean com.ttv.demo  Bundle com.ttv.demo  ContentAdapter com.ttv.demo  ContentFragment com.ttv.demo  Context com.ttv.demo  CoordinatorLayout com.ttv.demo  CustomAppBarLayoutBehavior com.ttv.demo  Fragment com.ttv.demo  FragmentActivity com.ttv.demo  FragmentStateAdapter com.ttv.demo  Handler com.ttv.demo  Int com.ttv.demo  IntArray com.ttv.demo  LayoutInflater com.ttv.demo  LinearLayoutManager com.ttv.demo  List com.ttv.demo  Looper com.ttv.demo  MainActivity com.ttv.demo  Math com.ttv.demo  NestedScrollView com.ttv.demo  R com.ttv.demo  RecyclerView com.ttv.demo  SmartRefreshLayout com.ttv.demo  StickyTabBehavior com.ttv.demo  String com.ttv.demo  	TabLayout com.ttv.demo  TabLayoutMediator com.ttv.demo  TextView com.ttv.demo  View com.ttv.demo  
ViewCompat com.ttv.demo  	ViewGroup com.ttv.demo  
ViewHolder com.ttv.demo  
ViewPager2 com.ttv.demo  ViewPagerAdapter com.ttv.demo  animateTabToOneThirdPosition com.ttv.demo  let com.ttv.demo  
mutableListOf com.ttv.demo  newInstance com.ttv.demo  stickyTabLayout com.ttv.demo  	tabLayout com.ttv.demo  until com.ttv.demo  
viewPager2 com.ttv.demo  Behavior com.ttv.demo.AppBarLayout  LayoutParams com.ttv.demo.AppBarLayout  
ViewHolder com.ttv.demo.ContentAdapter  ARG_POSITION com.ttv.demo.ContentFragment  Bundle com.ttv.demo.ContentFragment  	Companion com.ttv.demo.ContentFragment  ContentAdapter com.ttv.demo.ContentFragment  ContentFragment com.ttv.demo.ContentFragment  Int com.ttv.demo.ContentFragment  LayoutInflater com.ttv.demo.ContentFragment  LinearLayoutManager com.ttv.demo.ContentFragment  List com.ttv.demo.ContentFragment  R com.ttv.demo.ContentFragment  RecyclerView com.ttv.demo.ContentFragment  String com.ttv.demo.ContentFragment  TextView com.ttv.demo.ContentFragment  View com.ttv.demo.ContentFragment  	ViewGroup com.ttv.demo.ContentFragment  
ViewHolder com.ttv.demo.ContentFragment  	arguments com.ttv.demo.ContentFragment  context com.ttv.demo.ContentFragment  generateMockData com.ttv.demo.ContentFragment  
getTabName com.ttv.demo.ContentFragment  let com.ttv.demo.ContentFragment  
mutableListOf com.ttv.demo.ContentFragment  newInstance com.ttv.demo.ContentFragment  position com.ttv.demo.ContentFragment  ARG_POSITION &com.ttv.demo.ContentFragment.Companion  Bundle &com.ttv.demo.ContentFragment.Companion  ContentAdapter &com.ttv.demo.ContentFragment.Companion  ContentFragment &com.ttv.demo.ContentFragment.Companion  LayoutInflater &com.ttv.demo.ContentFragment.Companion  LinearLayoutManager &com.ttv.demo.ContentFragment.Companion  R &com.ttv.demo.ContentFragment.Companion  
ViewHolder &com.ttv.demo.ContentFragment.Companion  let &com.ttv.demo.ContentFragment.Companion  
mutableListOf &com.ttv.demo.ContentFragment.Companion  newInstance &com.ttv.demo.ContentFragment.Companion  Int +com.ttv.demo.ContentFragment.ContentAdapter  LayoutInflater +com.ttv.demo.ContentFragment.ContentAdapter  List +com.ttv.demo.ContentFragment.ContentAdapter  R +com.ttv.demo.ContentFragment.ContentAdapter  RecyclerView +com.ttv.demo.ContentFragment.ContentAdapter  String +com.ttv.demo.ContentFragment.ContentAdapter  TextView +com.ttv.demo.ContentFragment.ContentAdapter  View +com.ttv.demo.ContentFragment.ContentAdapter  	ViewGroup +com.ttv.demo.ContentFragment.ContentAdapter  
ViewHolder +com.ttv.demo.ContentFragment.ContentAdapter  data +com.ttv.demo.ContentFragment.ContentAdapter  
ViewHolder 8com.ttv.demo.ContentFragment.ContentAdapter.RecyclerView  R 6com.ttv.demo.ContentFragment.ContentAdapter.ViewHolder  textView 6com.ttv.demo.ContentFragment.ContentAdapter.ViewHolder  Adapter )com.ttv.demo.ContentFragment.RecyclerView  
ViewHolder )com.ttv.demo.ContentFragment.RecyclerView  Behavior com.ttv.demo.CoordinatorLayout  isTabSticky 'com.ttv.demo.CustomAppBarLayoutBehavior  targetStickyPosition 'com.ttv.demo.CustomAppBarLayoutBehavior  AppBarLayout com.ttv.demo.MainActivity  Bundle com.ttv.demo.MainActivity  ContentFragment com.ttv.demo.MainActivity  CoordinatorLayout com.ttv.demo.MainActivity  Fragment com.ttv.demo.MainActivity  FragmentActivity com.ttv.demo.MainActivity  FragmentStateAdapter com.ttv.demo.MainActivity  Handler com.ttv.demo.MainActivity  Int com.ttv.demo.MainActivity  IntArray com.ttv.demo.MainActivity  Looper com.ttv.demo.MainActivity  Math com.ttv.demo.MainActivity  NestedScrollView com.ttv.demo.MainActivity  R com.ttv.demo.MainActivity  SmartRefreshLayout com.ttv.demo.MainActivity  String com.ttv.demo.MainActivity  	TabLayout com.ttv.demo.MainActivity  TabLayoutMediator com.ttv.demo.MainActivity  View com.ttv.demo.MainActivity  
ViewPager2 com.ttv.demo.MainActivity  ViewPagerAdapter com.ttv.demo.MainActivity  animateTabToOneThirdPosition com.ttv.demo.MainActivity  appBarLayout com.ttv.demo.MainActivity  $checkTabVisibilityAndToggleStickyTab com.ttv.demo.MainActivity  coordinatorLayout com.ttv.demo.MainActivity  findViewById com.ttv.demo.MainActivity  getTabTitle com.ttv.demo.MainActivity  handleReleaseButtonClick com.ttv.demo.MainActivity  hasUserScrolled com.ttv.demo.MainActivity  
hideStickyTab com.ttv.demo.MainActivity  	initViews com.ttv.demo.MainActivity  isTabClickAnimating com.ttv.demo.MainActivity  nestedScrollView com.ttv.demo.MainActivity  newInstance com.ttv.demo.MainActivity  originalReleaseBtn com.ttv.demo.MainActivity  	resources com.ttv.demo.MainActivity  setContentView com.ttv.demo.MainActivity  setupNestedScrollViewListener com.ttv.demo.MainActivity  setupRefreshLayout com.ttv.demo.MainActivity  setupScrollBehavior com.ttv.demo.MainActivity  setupStickyTabLayout com.ttv.demo.MainActivity  setupTabLayout com.ttv.demo.MainActivity  setupTabStickyBehavior com.ttv.demo.MainActivity  setupTabSynchronization com.ttv.demo.MainActivity  setupViewPager com.ttv.demo.MainActivity  
showStickyTab com.ttv.demo.MainActivity  smartRefreshLayout com.ttv.demo.MainActivity  stickyReleaseBtn com.ttv.demo.MainActivity  stickyTabContainer com.ttv.demo.MainActivity  stickyTabLayout com.ttv.demo.MainActivity  tabContainer com.ttv.demo.MainActivity  	tabLayout com.ttv.demo.MainActivity  until com.ttv.demo.MainActivity  
viewPager2 com.ttv.demo.MainActivity  LayoutParams &com.ttv.demo.MainActivity.AppBarLayout  OnTabSelectedListener #com.ttv.demo.MainActivity.TabLayout  Tab #com.ttv.demo.MainActivity.TabLayout  OnPageChangeCallback $com.ttv.demo.MainActivity.ViewPager2  ContentFragment *com.ttv.demo.MainActivity.ViewPagerAdapter  newInstance *com.ttv.demo.MainActivity.ViewPagerAdapter  appBarLayout com.ttv.demo.R.id  clLayout com.ttv.demo.R.id  coIndicator com.ttv.demo.R.id  fluent_content_nsv com.ttv.demo.R.id  fluent_refresh_layout com.ttv.demo.R.id  home_feed_linear_tabLayout com.ttv.demo.R.id  home_release_btn com.ttv.demo.R.id  home_viewPager com.ttv.demo.R.id  recyclerView com.ttv.demo.R.id  sticky_release_btn com.ttv.demo.R.id  sticky_tab_container com.ttv.demo.R.id  sticky_tab_layout com.ttv.demo.R.id  textView com.ttv.demo.R.id  
activity_main com.ttv.demo.R.layout  fragment_content com.ttv.demo.R.layout  item_content com.ttv.demo.R.layout  Adapter com.ttv.demo.RecyclerView  
ViewHolder com.ttv.demo.RecyclerView  Math com.ttv.demo.StickyTabBehavior  
ViewCompat com.ttv.demo.StickyTabBehavior  isSticky com.ttv.demo.StickyTabBehavior  
targetStickyY com.ttv.demo.StickyTabBehavior  OnTabSelectedListener com.ttv.demo.TabLayout  Tab com.ttv.demo.TabLayout  OnPageChangeCallback com.ttv.demo.ViewPager2  Runnable 	java.lang  abs java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function5 kotlin  IntArray kotlin  Nothing kotlin  let kotlin  not kotlin.Boolean  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  toFloat 
kotlin.Int  get kotlin.IntArray  IntIterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  
mutableListOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.List  size kotlin.collections.List  add kotlin.collections.MutableList  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  CoordinatorLayout android.app.Activity  StickyTabBehavior android.app.Activity  LayoutParams &android.app.Activity.CoordinatorLayout  CoordinatorLayout android.content.Context  StickyTabBehavior android.content.Context  LayoutParams )android.content.Context.CoordinatorLayout  CoordinatorLayout android.content.ContextWrapper  StickyTabBehavior android.content.ContextWrapper  LayoutParams 0android.content.ContextWrapper.CoordinatorLayout  CoordinatorLayout  android.view.ContextThemeWrapper  StickyTabBehavior  android.view.ContextThemeWrapper  LayoutParams 2android.view.ContextThemeWrapper.CoordinatorLayout  y android.view.View  y !android.view.ViewPropertyAnimator  CoordinatorLayout #androidx.activity.ComponentActivity  StickyTabBehavior #androidx.activity.ComponentActivity  LayoutParams 5androidx.activity.ComponentActivity.CoordinatorLayout  CoordinatorLayout (androidx.appcompat.app.AppCompatActivity  StickyTabBehavior (androidx.appcompat.app.AppCompatActivity  LayoutParams :androidx.appcompat.app.AppCompatActivity.CoordinatorLayout  LayoutParams 3androidx.coordinatorlayout.widget.CoordinatorLayout  abs <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  max <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  min <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  behavior @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  StickyTabBehavior #androidx.core.app.ComponentActivity  LayoutParams 5androidx.core.app.ComponentActivity.CoordinatorLayout  CoordinatorLayout &androidx.fragment.app.FragmentActivity  StickyTabBehavior &androidx.fragment.app.FragmentActivity  LayoutParams 8androidx.fragment.app.FragmentActivity.CoordinatorLayout  abs com.ttv.demo  max com.ttv.demo  min com.ttv.demo  LayoutParams com.ttv.demo.CoordinatorLayout  StickyTabBehavior com.ttv.demo.MainActivity  getStickyTabBehavior com.ttv.demo.MainActivity  triggerTabStickyPosition com.ttv.demo.MainActivity  LayoutParams +com.ttv.demo.MainActivity.CoordinatorLayout  abs com.ttv.demo.StickyTabBehavior  
isInitialized com.ttv.demo.StickyTabBehavior  max com.ttv.demo.StickyTabBehavior  min com.ttv.demo.StickyTabBehavior  	originalY com.ttv.demo.StickyTabBehavior  resetToOriginalPosition com.ttv.demo.StickyTabBehavior  triggerStickyPosition com.ttv.demo.StickyTabBehavior  plus kotlin.Float  times kotlin.Float  max kotlin.collections  min kotlin.collections  abs kotlin.math  max kotlin.math  min kotlin.math  max kotlin.sequences  min kotlin.sequences  max kotlin.text  min kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         