package com.ttv.demo

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.scwang.smart.refresh.layout.SmartRefreshLayout

class MainActivity : AppCompatActivity() {
    
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var tabLayout: TabLayout
    private lateinit var viewPager2: ViewPager2
    private lateinit var smartRefreshLayout: SmartRefreshLayout
    private lateinit var nestedScrollView: NestedScrollView
    private lateinit var tabContainer: View
    
    // 双Tab栏相关视图
    private lateinit var stickyTabContainer: View
    private lateinit var stickyTabLayout: TabLayout
    private lateinit var stickyReleaseBtn: View
    private lateinit var originalReleaseBtn: View

    // 状态标记：是否正在执行Tab点击动画
    private var isTabClickAnimating = false
    // 记录用户是否开始滚动，用于恢复正常滚动行为
    private var hasUserScrolled = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initViews()
        setupViewPager()
        setupTabLayout()
        setupStickyTabLayout()
        setupTabSynchronization()
        setupRefreshLayout()
        setupScrollBehavior()
    }
    
    private fun initViews() {
        coordinatorLayout = findViewById(R.id.clLayout)
        appBarLayout = findViewById(R.id.appBarLayout)
        tabLayout = findViewById(R.id.coIndicator) // Tab栏A（内联Tab）
        viewPager2 = findViewById(R.id.home_viewPager)
        smartRefreshLayout = findViewById(R.id.fluent_refresh_layout)
        nestedScrollView = findViewById(R.id.fluent_content_nsv)
        tabContainer = findViewById(R.id.home_feed_linear_tabLayout)
        
        // 双Tab栏相关视图初始化
        stickyTabContainer = findViewById(R.id.sticky_tab_container)
        stickyTabLayout = findViewById(R.id.sticky_tab_layout) // Tab栏B（吸底Tab）
        stickyReleaseBtn = findViewById(R.id.sticky_release_btn)
        originalReleaseBtn = findViewById(R.id.home_release_btn)
    }
    
    private fun setupViewPager() {
        val adapter = ViewPagerAdapter(this)
        viewPager2.adapter = adapter
    }
    
    private fun setupTabLayout() {
        // 连接TabLayout和ViewPager2
        TabLayoutMediator(tabLayout, viewPager2) { tab, position ->
            tab.text = getTabTitle(position)
        }.attach()

        // 添加Tab选择监听器，实现点击跳转到屏幕1/3处的效果
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                // 实现Tab点击跳转到屏幕1/3处的效果
                animateTabToOneThirdPosition()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // 重新选择时也触发跳转效果
                animateTabToOneThirdPosition()
            }
        })
    }
    
    private fun setupStickyTabLayout() {
        // 为吸底Tab栏设置相同的Tab标题
        for (i in 0 until 5) {
            stickyTabLayout.addTab(stickyTabLayout.newTab().setText(getTabTitle(i)))
        }
    }
    
    /**
     * 设置双Tab栏的状态同步机制
     */
    private fun setupTabSynchronization() {
        // 标记是否正在同步状态，避免循环触发
        var isSyncing = false

        // 吸底Tab栏B的选择监听器
        stickyTabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (!isSyncing && tab != null) {
                    isSyncing = true
                    // 同步到内联Tab栏A
                    tabLayout.selectTab(tabLayout.getTabAt(tab.position))
                    // 切换ViewPager2页面
                    viewPager2.currentItem = tab.position
                    // 触发Tab点击移动到1/3位置的效果
                    animateTabToOneThirdPosition()
                    isSyncing = false
                }
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {
                if (!isSyncing && tab != null) {
                    // 重新选择时也触发跳转效果
                    animateTabToOneThirdPosition()
                }
            }
        })
        
        // ViewPager2页面变化监听器，同步两个Tab栏的状态
        viewPager2.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                if (!isSyncing) {
                    isSyncing = true
                    // 同步两个Tab栏的选中状态
                    tabLayout.selectTab(tabLayout.getTabAt(position))
                    stickyTabLayout.selectTab(stickyTabLayout.getTabAt(position))
                    isSyncing = false
                }
            }
        })
        
        // 发布按钮点击事件同步
        originalReleaseBtn.setOnClickListener { handleReleaseButtonClick() }
        stickyReleaseBtn.setOnClickListener { handleReleaseButtonClick() }
    }
    
    /**
     * 处理发布按钮点击事件
     */
    private fun handleReleaseButtonClick() {
        // 这里添加发布按钮的具体逻辑
        // 目前只是示例，可以根据实际需求修改
    }
    
    private fun setupRefreshLayout() {
        smartRefreshLayout.setOnRefreshListener { refreshLayout ->
            // 模拟刷新数据
            Handler(Looper.getMainLooper()).postDelayed({
                refreshLayout.finishRefresh()
            }, 2000)
        }
    }
    
    private fun getTabTitle(position: Int): String {
        return when (position) {
            0 -> "首页"
            1 -> "发现"
            2 -> "关注"
            3 -> "推荐"
            4 -> "热门"
            else -> "Tab ${position + 1}"
        }
    }

    private fun setupScrollBehavior() {
        // 通过代码实现Tab吸底效果
        setupTabStickyBehavior()
        // 设置NestedScrollView的滚动监听器
        setupNestedScrollViewListener()
    }

    /**
     * 通过代码实现Tab吸底效果
     * 简化版本：只处理基本的状态管理，移除复杂的位置动画逻辑
     */
    private fun setupTabStickyBehavior() {
        // 监听AppBarLayout的滚动状态
        appBarLayout.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { appBarLayout, verticalOffset ->
            val totalScrollRange = appBarLayout.totalScrollRange
            if (totalScrollRange == 0) return@OnOffsetChangedListener

            val scrollProgress = Math.abs(verticalOffset).toFloat() / totalScrollRange.toFloat()

            // 检测用户是否开始滚动
            if (scrollProgress > 0.05f) {
                hasUserScrolled = true
                // 如果用户开始滚动且不在动画过程中，恢复SmartRefreshLayout的正常位置
                if (!isTabClickAnimating) {
                    smartRefreshLayout.translationY = 0f
                }
            }

            // 实现Tab吸底效果：当滚动进度超过70%时，Tab保持可见
            if (scrollProgress > 0.7f) {
                // 确保Tab始终可见
                tabContainer.alpha = 1f
                tabContainer.visibility = View.VISIBLE
                // 计算Tab应该的位置（屏幕1/3处）
                val screenHeight = resources.displayMetrics.heightPixels
                val targetY = screenHeight / 3f
                val currentY = tabContainer.y

                // 如果Tab不在目标位置，平滑移动到目标位置
                if (Math.abs(currentY - targetY) > 10f) {
                    tabContainer.animate()
                        .y(targetY)
                        .setDuration(200)
                        .start()
                }
            }
        })
    }

    /**
     * 实现Tab点击跳转到屏幕1/3处的动画效果
     * 基于官方文档的最佳实践，使用标准的动画API
     */
    private fun animateTabToOneThirdPosition() {
        val screenHeight = resources.displayMetrics.heightPixels
        val targetY = screenHeight / 3f

        // 获取当前Tab容器的屏幕位置
        val location = IntArray(2)
        tabContainer.getLocationOnScreen(location)
        val currentTabY = location[1].toFloat()

        // 计算需要移动的距离（正值向下，负值向上）
        val moveDistance = targetY - currentTabY

        // 定义位置容差范围（像素），避免微小差异导致的不必要动画
        val positionTolerance = 15f

        // 如果Tab已经在目标位置附近，无需移动
        if (Math.abs(moveDistance) <= positionTolerance) {
            tabContainer.alpha = 1f
            tabContainer.visibility = View.VISIBLE
            return
        }

        // 标记正在执行点击动画
        isTabClickAnimating = true
        hasUserScrolled = false

        // 确保Tab在移动过程中始终可见
        tabContainer.alpha = 1f
        tabContainer.visibility = View.VISIBLE

        // 通过移动SmartRefreshLayout来实现精确定位
        smartRefreshLayout.animate()
            .translationY(moveDistance)
            .setDuration(300)
            .withStartAction {
                // 动画开始时确保Tab可见
                tabContainer.alpha = 1f
                tabContainer.visibility = View.VISIBLE
            }
            .withEndAction {
                // 动画结束后标记动画完成
                isTabClickAnimating = false
                tabContainer.alpha = 1f
                tabContainer.visibility = View.VISIBLE
            }
            .start()
    }

    /**
     * 设置NestedScrollView的滚动监听器
     * 检测用户滚动行为，在用户滚动时恢复正常的滚动联动
     * 同时控制吸底Tab栏的显隐
     */
    private fun setupNestedScrollViewListener() {
        nestedScrollView.setOnScrollChangeListener { _, _, scrollY, _, oldScrollY ->
            // 检测到用户滚动行为
            if (Math.abs(scrollY - oldScrollY) > 5 && !isTabClickAnimating) {
                hasUserScrolled = true
                // 如果用户开始滚动，逐渐恢复SmartRefreshLayout的正常位置
                if (smartRefreshLayout.translationY != 0f) {
                    smartRefreshLayout.animate()
                        .translationY(0f)
                        .setDuration(200)
                        .start()
                }
            }

            // 检测Tab栏A的屏幕位置，控制吸底Tab栏B的显隐
            checkTabVisibilityAndToggleStickyTab()
        }
    }

    /**
     * 检测Tab栏A的屏幕位置，控制吸底Tab栏B的显隐
     */
    private fun checkTabVisibilityAndToggleStickyTab() {
        // 获取Tab栏A在屏幕中的位置
        val location = IntArray(2)
        tabContainer.getLocationOnScreen(location)
        val tabContainerTop = location[1]
        val tabContainerBottom = tabContainerTop + tabContainer.height

        // 获取屏幕高度
        val screenHeight = resources.displayMetrics.heightPixels

        // 判断Tab栏A是否在屏幕可见区域内
        // 触发条件：当Tab栏A的底部小于屏幕顶部时（即Tab栏A完全滚出屏幕上方）
        val isTabAVisible = tabContainerBottom > 0 && tabContainerTop < screenHeight

        // 根据Tab栏A的可见性控制吸底Tab栏B的显隐
        if (!isTabAVisible && stickyTabContainer.visibility == View.GONE) {
            // Tab栏A不可见，显示吸底Tab栏B
            showStickyTab()
        } else if (isTabAVisible && stickyTabContainer.visibility == View.VISIBLE) {
            // Tab栏A可见，隐藏吸底Tab栏B
            hideStickyTab()
        }
    }

    /**
     * 显示吸底Tab栏B（带动画效果）
     */
    private fun showStickyTab() {
        stickyTabContainer.visibility = View.VISIBLE
        stickyTabContainer.alpha = 0f
        stickyTabContainer.animate()
            .alpha(1f)
            .setDuration(250)
            .start()
    }

    /**
     * 隐藏吸底Tab栏B（带动画效果）
     */
    private fun hideStickyTab() {
        stickyTabContainer.animate()
            .alpha(0f)
            .setDuration(250)
            .withEndAction {
                stickyTabContainer.visibility = View.GONE
            }
            .start()
    }

    /**
     * 手动触发Tab吸底到屏幕1/3位置
     */
    private fun triggerTabStickyPosition() {
        val screenHeight = resources.displayMetrics.heightPixels
        val targetY = screenHeight / 3f

        tabContainer.animate()
            .y(targetY)
            .setDuration(300)
            .start()
    }

    /**
     * 重置Tab位置到正常状态，恢复正常的滚动行为
     * 可以在需要时调用此方法恢复正常的滚动行为
     */
    private fun resetTabPosition() {
        hasUserScrolled = false
        isTabClickAnimating = false
        smartRefreshLayout.animate()
            .translationY(0f)
            .setDuration(300)
            .start()
    }

    // ViewPager2适配器
    private class ViewPagerAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
        override fun getItemCount(): Int = 5

        override fun createFragment(position: Int): Fragment {
            return ContentFragment.newInstance(position)
        }
    }
}
