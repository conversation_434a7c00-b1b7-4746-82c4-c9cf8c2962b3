<?xml version="1.0" encoding="utf-8"?>
<!-- 严格按照原始XML结构，将自定义组件替换为标准Android组件 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_l1"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource"
    tools:context=".MainActivity">

    <!--给缓存WebView准备的ViewGroup-->
    <FrameLayout
        android:id="@+id/webview_pool_container"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <!--navigation 区域 状态栏和引擎容器-->
    <RelativeLayout
        android:id="@+id/navigation_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="MissingDefaultResource">

        <!--状态栏-->
        <View
            android:id="@+id/home_status_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_25"
            android:background="@color/status_bar_color" />

        <!--navigation 容器区域-->
        <LinearLayout
            android:id="@+id/home_navigation_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/home_status_bar"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Demo应用"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

        </LinearLayout>

    </RelativeLayout>

    <!-- fluent 页面流模块区域 -->
    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/fluent_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/navigation_container"
        android:layout_marginTop="@dimen/dimen_12">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/clLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:elevation="0dp">

                <!-- 替换自定义MyNestedScrollView为标准NestedScrollView -->
                <androidx.core.widget.NestedScrollView
                    android:id="@+id/fluent_content_nsv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="blocksDescendants"
                    android:fillViewport="true"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layout_scrollFlags="scroll">

                    <LinearLayout
                        android:id="@+id/fluent_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- 添加模拟内容 -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="300dp"
                            android:background="@color/header_background"
                            android:gravity="center"
                            android:text="头部内容区域\n(向上滚动查看Tab吸底效果)"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:layout_marginTop="16dp"
                            android:background="@color/content_background" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="150dp"
                            android:layout_marginTop="16dp"
                            android:background="@color/header_background"
                            android:gravity="center"
                            android:text="更多内容区域"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp" />

                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>

                <!--  feed tab 组件 -->
                <LinearLayout
                    android:id="@+id/home_feed_linear_tabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:background="@color/tab_background"
                    android:elevation="4dp"
                    app:layout_scrollFlags="scroll|enterAlways|snap">

                    <!-- 替换自定义CoIndicator为标准TabLayout -->
                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/coIndicator"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dimen_40"
                        android:layout_weight="1"
                        android:paddingLeft="@dimen/dimen_8"
                        app:tabGravity="start"
                        app:tabIndicatorColor="@color/button_blue_fill"
                        app:tabIndicatorHeight="3dp"
                        app:tabMaxWidth="@dimen/dimen_200"
                        app:tabMinWidth="@dimen/dimen_20"
                        app:tabMode="scrollable"
                        app:tabPaddingEnd="@dimen/dimen_8"
                        app:tabPaddingStart="@dimen/dimen_8"
                        app:tabRippleColor="@android:color/transparent"
                        app:tabSelectedTextColor="@color/button_blue_fill"
                        app:tabTextColor="@color/text_l3" />

                    <!--  TabLayout 发布按钮 - 替换自定义RoundLinearLayout为标准LinearLayout -->
                    <LinearLayout
                        android:id="@+id/home_release_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dimen_20"
                        android:layout_marginEnd="@dimen/dimen_16"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/dimen_10"
                        android:paddingEnd="@dimen/dimen_10"
                        android:visibility="visible"
                        android:background="@drawable/release_button_background">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dimen_20"
                            android:gravity="center"
                            android:text="@string/n_send_comment"
                            android:textColor="@color/button_blue_fill"
                            android:textSize="@dimen/global_text_size_12" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.appbar.AppBarLayout>

            <!--  feed view pager 组件 -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/home_viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdge="none"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!-- Tab栏B（吸底Tab）- 固定在屏幕底部，默认隐藏 -->
    <LinearLayout
        android:id="@+id/sticky_tab_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="center_vertical"
        android:background="@color/tab_background"
        android:elevation="8dp"
        android:visibility="gone">

        <!-- 吸底TabLayout -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/sticky_tab_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_40"
            android:layout_weight="1"
            android:paddingLeft="@dimen/dimen_8"
            app:tabGravity="start"
            app:tabIndicatorColor="@color/button_blue_fill"
            app:tabIndicatorHeight="3dp"
            app:tabMaxWidth="@dimen/dimen_200"
            app:tabMinWidth="@dimen/dimen_20"
            app:tabMode="scrollable"
            app:tabPaddingEnd="@dimen/dimen_8"
            app:tabPaddingStart="@dimen/dimen_8"
            app:tabRippleColor="@android:color/transparent"
            app:tabSelectedTextColor="@color/button_blue_fill"
            app:tabTextColor="@color/text_l3" />

        <!-- 吸底发布按钮 -->
        <LinearLayout
            android:id="@+id/sticky_release_btn"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_20"
            android:layout_marginEnd="@dimen/dimen_16"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dimen_10"
            android:paddingEnd="@dimen/dimen_10"
            android:visibility="visible"
            android:background="@drawable/release_button_background">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dimen_20"
                android:gravity="center"
                android:text="@string/n_send_comment"
                android:textColor="@color/button_blue_fill"
                android:textSize="@dimen/global_text_size_12" />
        </LinearLayout>
    </LinearLayout>

    <!--动画层-->
    <RelativeLayout
        android:id="@+id/rl_new_hand_area_animation_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</RelativeLayout>
