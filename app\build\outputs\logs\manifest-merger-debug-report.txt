-- Merging decision tree log ---
manifest
ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:2:1-27:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6678e3bdc760601f94ecc08acf1a57f\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b0b74aab88978b600bd7a370e2fff9b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4b2a8d966dfae6f4abece8f5f7c049\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147e0183abcca07bfea377b96a799c57\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01c55d5bcc4fea1898b00d8a81c66c03\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6040f8a5426f501d90ca44979f09b8e2\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b69c98b28c33db478bc05e366fa93f86\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\799f89f1334336a8432ca0f734c76449\transformed\jetified-fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c1d6cc5c2630965de37c0ab2222fa8\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5525776a25ad89d237233be4c604346d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eabfd7aa1310213678068798bb0bb6ef\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3b2a99bde9544c8786a5b2e496d3650\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d64907814ebed0f116f387a7279ba89\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0566ef5dde78c610fab6f9425f77ca2c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8989a60eb642719bd938a3ab153835\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a3307ee8275b6ba0adb871a85d73743\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa89d15c53ed7b94bf3552ad4d796b8c\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61de88b9d4b462029b8ba9e7f20e719\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\987300b68855958521f2fc44879a95a6\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f93a821497952d16af786ac53d144e4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d26d0bdcff1e66482c4e25567167bf8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cd52a1048ae456d9215df9531618071\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac381c94a4eb093ff66ca81fa4204297\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6033fca38f101074d450e9983ebde2ea\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\070a7337eed83ac4d5e72d9568d60398\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab982460666533be8db033bd2983a28\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2df8bb8d2f5e58e254e2ae522021247\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f4e2f083277b37e7f78f6766c87dcf\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec61dc82bb6b2562309989f626a338c2\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a3c7b3651f81a02b66fd5798c94423d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b342d2cb2636d310adb1f002e26e68c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5c695554e618a07fa7d3b3724af836c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98ca240d55c18b1147d2e40bb6e86ec5\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b174fd0261a4eae51671d31516f6a601\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6788511c9442915d19b005745a92db3a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebb5d172a429de0e304e8649ab12fb9e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b936d01a5290b04b67748df7396178\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5606692a0e4e547c0b76059e6947ba\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7271593b3c13135773c3a6211e8a53\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6c379ab8c81a47918af5f5221782bb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59dd315dbed696ad85132c36f2bd3304\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfd94201e110635673dd74ba2490f517\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a68db7028119d00933f0f6a0ad81105\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fed9e2d39b48e252a2fd9f5e098b23cb\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.scwang90:refresh-layout-kernel:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd2823d28a4b734fbd36b87d118d3c53\transformed\jetified-refresh-layout-kernel-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [io.github.scwang90:refresh-header-classics:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\875ae5e099ba090ef9f2901040444925\transformed\jetified-refresh-header-classics-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [io.github.scwang90:refresh-footer-classics:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a400ec125baae016e878759289529e08\transformed\jetified-refresh-footer-classics-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [io.github.scwang90:refresh-drawable-paint:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc4e3718f01aace957057338f3e4c52\transformed\jetified-refresh-drawable-paint-2.1.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:5:5-25:19
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:5:5-25:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6678e3bdc760601f94ecc08acf1a57f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6678e3bdc760601f94ecc08acf1a57f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b0b74aab88978b600bd7a370e2fff9b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b0b74aab88978b600bd7a370e2fff9b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5606692a0e4e547c0b76059e6947ba\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5606692a0e4e547c0b76059e6947ba\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:13:9-42
	android:dataExtractionRules
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:7:9-65
activity#com.ttv.demo.MainActivity
ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:16:9-23:20
	android:exported
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:18:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:19:13-22:29
action#android.intent.action.MAIN
ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:20:17-69
	android:name
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:20:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:21:17-77
	android:name
		ADDED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml:21:27-74
uses-sdk
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6678e3bdc760601f94ecc08acf1a57f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6678e3bdc760601f94ecc08acf1a57f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b0b74aab88978b600bd7a370e2fff9b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b0b74aab88978b600bd7a370e2fff9b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4b2a8d966dfae6f4abece8f5f7c049\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4b2a8d966dfae6f4abece8f5f7c049\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147e0183abcca07bfea377b96a799c57\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147e0183abcca07bfea377b96a799c57\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01c55d5bcc4fea1898b00d8a81c66c03\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01c55d5bcc4fea1898b00d8a81c66c03\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6040f8a5426f501d90ca44979f09b8e2\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6040f8a5426f501d90ca44979f09b8e2\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b69c98b28c33db478bc05e366fa93f86\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b69c98b28c33db478bc05e366fa93f86\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\799f89f1334336a8432ca0f734c76449\transformed\jetified-fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\799f89f1334336a8432ca0f734c76449\transformed\jetified-fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c1d6cc5c2630965de37c0ab2222fa8\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c1d6cc5c2630965de37c0ab2222fa8\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5525776a25ad89d237233be4c604346d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5525776a25ad89d237233be4c604346d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eabfd7aa1310213678068798bb0bb6ef\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eabfd7aa1310213678068798bb0bb6ef\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3b2a99bde9544c8786a5b2e496d3650\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3b2a99bde9544c8786a5b2e496d3650\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d64907814ebed0f116f387a7279ba89\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d64907814ebed0f116f387a7279ba89\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0566ef5dde78c610fab6f9425f77ca2c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0566ef5dde78c610fab6f9425f77ca2c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8989a60eb642719bd938a3ab153835\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a8989a60eb642719bd938a3ab153835\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a3307ee8275b6ba0adb871a85d73743\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a3307ee8275b6ba0adb871a85d73743\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa89d15c53ed7b94bf3552ad4d796b8c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa89d15c53ed7b94bf3552ad4d796b8c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61de88b9d4b462029b8ba9e7f20e719\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61de88b9d4b462029b8ba9e7f20e719\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\987300b68855958521f2fc44879a95a6\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\987300b68855958521f2fc44879a95a6\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f93a821497952d16af786ac53d144e4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f93a821497952d16af786ac53d144e4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d26d0bdcff1e66482c4e25567167bf8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d26d0bdcff1e66482c4e25567167bf8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cd52a1048ae456d9215df9531618071\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cd52a1048ae456d9215df9531618071\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac381c94a4eb093ff66ca81fa4204297\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac381c94a4eb093ff66ca81fa4204297\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6033fca38f101074d450e9983ebde2ea\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6033fca38f101074d450e9983ebde2ea\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\070a7337eed83ac4d5e72d9568d60398\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\070a7337eed83ac4d5e72d9568d60398\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab982460666533be8db033bd2983a28\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ab982460666533be8db033bd2983a28\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2df8bb8d2f5e58e254e2ae522021247\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2df8bb8d2f5e58e254e2ae522021247\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f4e2f083277b37e7f78f6766c87dcf\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3f4e2f083277b37e7f78f6766c87dcf\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec61dc82bb6b2562309989f626a338c2\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec61dc82bb6b2562309989f626a338c2\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a3c7b3651f81a02b66fd5798c94423d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a3c7b3651f81a02b66fd5798c94423d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b342d2cb2636d310adb1f002e26e68c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b342d2cb2636d310adb1f002e26e68c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5c695554e618a07fa7d3b3724af836c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5c695554e618a07fa7d3b3724af836c\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98ca240d55c18b1147d2e40bb6e86ec5\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98ca240d55c18b1147d2e40bb6e86ec5\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b174fd0261a4eae51671d31516f6a601\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b174fd0261a4eae51671d31516f6a601\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6788511c9442915d19b005745a92db3a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6788511c9442915d19b005745a92db3a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebb5d172a429de0e304e8649ab12fb9e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebb5d172a429de0e304e8649ab12fb9e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b936d01a5290b04b67748df7396178\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b936d01a5290b04b67748df7396178\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5606692a0e4e547c0b76059e6947ba\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5606692a0e4e547c0b76059e6947ba\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7271593b3c13135773c3a6211e8a53\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7271593b3c13135773c3a6211e8a53\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6c379ab8c81a47918af5f5221782bb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6c379ab8c81a47918af5f5221782bb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59dd315dbed696ad85132c36f2bd3304\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59dd315dbed696ad85132c36f2bd3304\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfd94201e110635673dd74ba2490f517\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfd94201e110635673dd74ba2490f517\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a68db7028119d00933f0f6a0ad81105\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a68db7028119d00933f0f6a0ad81105\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fed9e2d39b48e252a2fd9f5e098b23cb\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fed9e2d39b48e252a2fd9f5e098b23cb\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.scwang90:refresh-layout-kernel:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd2823d28a4b734fbd36b87d118d3c53\transformed\jetified-refresh-layout-kernel-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-layout-kernel:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd2823d28a4b734fbd36b87d118d3c53\transformed\jetified-refresh-layout-kernel-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-header-classics:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\875ae5e099ba090ef9f2901040444925\transformed\jetified-refresh-header-classics-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-header-classics:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\875ae5e099ba090ef9f2901040444925\transformed\jetified-refresh-header-classics-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-footer-classics:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a400ec125baae016e878759289529e08\transformed\jetified-refresh-footer-classics-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-footer-classics:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a400ec125baae016e878759289529e08\transformed\jetified-refresh-footer-classics-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-drawable-paint:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc4e3718f01aace957057338f3e4c52\transformed\jetified-refresh-drawable-paint-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-drawable-paint:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcc4e3718f01aace957057338f3e4c52\transformed\jetified-refresh-drawable-paint-2.1.0\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\AndroidStudioProjects\demo\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809d2e49cf8e42a41074a4366eeadb27\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ac9ca86d448e33fbcb77c2fd56623d7\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.ttv.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.ttv.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07f183b10d45831b60fa2f6fedd0a58\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c9fd36db94d2407330ed6fc50caacb3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f48dabfb7317661264eea3162dc7ca3a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
