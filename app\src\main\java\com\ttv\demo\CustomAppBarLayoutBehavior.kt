package com.ttv.demo

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout

/**
 * 自定义AppBarLayout行为，实现Tab吸底到屏幕1/3处的效果
 */
class CustomAppBarLayoutBehavior(context: Context, attrs: AttributeSet) : AppBarLayout.Behavior(context, attrs) {

    private var isTabSticky = false
    private var targetStickyPosition = 0f

    override fun onLayoutChild(parent: CoordinatorLayout, abl: AppBarLayout, layoutDirection: Int): Boolean {
        val result = super.onLayoutChild(parent, abl, layoutDirection)
        
        // 计算屏幕1/3的位置
        val screenHeight = parent.height
        targetStickyPosition = screenHeight / 3f
        
        return result
    }

    override fun onDependentViewChanged(parent: CoordinatorLayout, child: AppBarLayout, dependency: View): Boolean {
        if (isTabSticky) {
            // 当Tab处于吸底状态时，固定在屏幕1/3处
            child.translationY = targetStickyPosition
            return true
        }
        return super.onDependentViewChanged(parent, child, dependency)
    }

    /**
     * 设置Tab吸底状态
     */
    fun setTabSticky(sticky: Boolean) {
        isTabSticky = sticky
    }
}
