package com.ttv.demo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class ContentFragment : Fragment() {
    
    private var position: Int = 0
    
    companion object {
        private const val ARG_POSITION = "position"
        
        fun newInstance(position: Int): ContentFragment {
            val fragment = ContentFragment()
            val args = Bundle()
            args.putInt(ARG_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            position = it.getInt(ARG_POSITION)
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_content, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = ContentAdapter(generateMockData())
    }
    
    private fun generateMockData(): List<String> {
        val data = mutableListOf<String>()
        val tabName = getTabName(position)
        
        for (i in 1..50) {
            data.add("${tabName} - 第${i}条内容数据")
        }
        return data
    }
    
    private fun getTabName(position: Int): String {
        return when (position) {
            0 -> "首页"
            1 -> "发现"
            2 -> "关注"
            3 -> "推荐"
            4 -> "热门"
            else -> "Tab ${position + 1}"
        }
    }
    
    // RecyclerView适配器
    private class ContentAdapter(private val data: List<String>) : RecyclerView.Adapter<ContentAdapter.ViewHolder>() {
        
        class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val textView: TextView = view.findViewById(R.id.textView)
        }
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_content, parent, false)
            return ViewHolder(view)
        }
        
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.textView.text = data[position]
        }
        
        override fun getItemCount(): Int = data.size
    }
}
