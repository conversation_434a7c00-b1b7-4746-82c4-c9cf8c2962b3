<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_color">#FFF5F5F5</color>
    <color name="background_l1">#FFF5F5F5</color>
    <color name="black">#FF000000</color>
    <color name="button_blue_fill">#FF6200EE</color>
    <color name="content_background">#FFEEEEEE</color>
    <color name="fragment_background">#FFFFFFFF</color>
    <color name="header_background">#FFE3F2FD</color>
    <color name="otc_fitter_item_unselect_color">#FFE8F5E8</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="release_button_background">#FFE8F5E8</color>
    <color name="release_button_text_color">#FF6200EE</color>
    <color name="status_bar_color">#FF6200EE</color>
    <color name="tab_background">#FFFFFFFF</color>
    <color name="tab_indicator_color">#FF6200EE</color>
    <color name="tab_selected_text_color">#FF6200EE</color>
    <color name="tab_text_color">#FF757575</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_l3">#FF757575</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    <color name="transparent">#00000000</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="dimen_10">10dp</dimen>
    <dimen name="dimen_12">12dp</dimen>
    <dimen name="dimen_16">16dp</dimen>
    <dimen name="dimen_20">20dp</dimen>
    <dimen name="dimen_200">200dp</dimen>
    <dimen name="dimen_25">25dp</dimen>
    <dimen name="dimen_40">40dp</dimen>
    <dimen name="dimen_8">8dp</dimen>
    <dimen name="global_corner_radius_2">8dp</dimen>
    <dimen name="global_text_size_12">12sp</dimen>
    <string name="app_name">demo</string>
    <string name="n_send_comment">发布</string>
    <style name="Theme.Demo" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>