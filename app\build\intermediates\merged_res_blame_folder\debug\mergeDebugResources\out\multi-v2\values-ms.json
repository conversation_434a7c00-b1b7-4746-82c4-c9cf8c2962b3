{"logs": [{"outputFile": "com.ttv.demo.app-mergeDebugResources-41:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a6678e3bdc760601f94ecc08acf1a57f\\transformed\\material-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1068,1133,1226,1291,1350,1437,1499,1561,1621,1687,1749,1803,1911,1968,2029,2084,2155,2275,2366,2443,2540,2625,2711,2859,2945,3031,3159,3247,3325,3378,3429,3495,3566,3644,3715,3794,3867,3943,4016,4087,4194,4286,4359,4449,4542,4616,4687,4778,4830,4910,4978,5062,5147,5209,5273,5336,5408,5512,5620,5716,5822,5879,5934,6020,6105,6183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "274,354,433,520,612,699,802,918,1001,1063,1128,1221,1286,1345,1432,1494,1556,1616,1682,1744,1798,1906,1963,2024,2079,2150,2270,2361,2438,2535,2620,2706,2854,2940,3026,3154,3242,3320,3373,3424,3490,3561,3639,3710,3789,3862,3938,4011,4082,4189,4281,4354,4444,4537,4611,4682,4773,4825,4905,4973,5057,5142,5204,5268,5331,5403,5507,5615,5711,5817,5874,5929,6015,6100,6178,6255"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3104,3183,3270,3362,4192,4295,4411,4494,4556,4621,4714,4779,4838,4925,4987,5049,5109,5175,5237,5291,5399,5456,5517,5572,5643,5763,5854,5931,6028,6113,6199,6347,6433,6519,6647,6735,6813,6866,6917,6983,7054,7132,7203,7282,7355,7431,7504,7575,7682,7774,7847,7937,8030,8104,8175,8266,8318,8398,8466,8550,8635,8697,8761,8824,8896,9000,9108,9204,9310,9367,9422,9589,9674,9752", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "324,3099,3178,3265,3357,3444,4290,4406,4489,4551,4616,4709,4774,4833,4920,4982,5044,5104,5170,5232,5286,5394,5451,5512,5567,5638,5758,5849,5926,6023,6108,6194,6342,6428,6514,6642,6730,6808,6861,6912,6978,7049,7127,7198,7277,7350,7426,7499,7570,7677,7769,7842,7932,8025,8099,8170,8261,8313,8393,8461,8545,8630,8692,8756,8819,8891,8995,9103,9199,9305,9362,9417,9503,9669,9747,9824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c07f183b10d45831b60fa2f6fedd0a58\\transformed\\core-1.16.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3544,3646,3743,3853,3959,4077,9829", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3539,3641,3738,3848,3954,4072,4187,9925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\147e0183abcca07bfea377b96a799c57\\transformed\\appcompat-1.7.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,9508", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,9584"}}]}]}